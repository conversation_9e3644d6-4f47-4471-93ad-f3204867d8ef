#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Data/Code/Vue/school-directory/school-directory/node_modules/.pnpm/vite@6.3.5/node_modules/vite/bin/node_modules:/Volumes/Data/Code/Vue/school-directory/school-directory/node_modules/.pnpm/vite@6.3.5/node_modules/vite/node_modules:/Volumes/Data/Code/Vue/school-directory/school-directory/node_modules/.pnpm/vite@6.3.5/node_modules:/Volumes/Data/Code/Vue/school-directory/school-directory/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Data/Code/Vue/school-directory/school-directory/node_modules/.pnpm/vite@6.3.5/node_modules/vite/bin/node_modules:/Volumes/Data/Code/Vue/school-directory/school-directory/node_modules/.pnpm/vite@6.3.5/node_modules/vite/node_modules:/Volumes/Data/Code/Vue/school-directory/school-directory/node_modules/.pnpm/vite@6.3.5/node_modules:/Volumes/Data/Code/Vue/school-directory/school-directory/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/vite.js" "$@"
else
  exec node  "$basedir/../../bin/vite.js" "$@"
fi
